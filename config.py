"""
邢不行｜策略分享会
选股策略框架𝓟𝓻𝓸

版权所有 ©️ 邢不行
微信: xbx1717

本代码仅供个人学习使用，未经授权不得复制、修改或用于商业用途。

Author: 邢不行
"""
import os
from pathlib import Path

from core.utils.path_kit import get_folder_path

# ====================================================================================================
# 1️⃣ 回测配置
# ====================================================================================================
# 回测数据的起始时间。如果因子使用滚动计算方法，在回测初期因子值可能为 NaN，实际的首次交易日期可能晚于这个起始时间。
start_date = '2015-01-01'
# 回测数据的结束时间。可以设为 None，表示使用最新数据；也可以指定具体日期，例如 '2024-11-01'。
end_date = None

# ====================================================================================================
# 2️⃣ 数据配置
# ====================================================================================================
data_center_path = r"D:\quantclass-data"  # 数据中心的文件夹
runtime_data_path = get_folder_path('data')  # 回测结果存放的的文件夹，默认为项目文件夹下的 data 文件夹，可以自定义

# ====================================================================================================
# 3️⃣ 策略配置
# ====================================================================================================
backtest_name = '方天画戟'  # 回测的策略组合的名称。可以自己任意取。一般建议，一个回测组，就是实盘中的一个账户。
# 策略明细
strategy_list = [
    {
        'name': '小市值_定风波',
        'hold_period': 'W',
        'offset_list': [0],
        'select_num': 5,
        'cap_weight': 1,
        'rebalance_time': '0955-0955',
        'factor_list': [('市值', True, '', 1)],
        'filter_list': [('成交额Mean', 5, 'val:>=5000_0000', True)],
        'timing': {
            'name': '定风波择时',  # 择时策略名称
            'limit': 100,  # 择时参数，定风波阈值。
            # 例如 '50' 表示前50个股票择时，'0.5' 表示前50%的股票择时，'0' 表示全部股票择时；定风波开仓比例，例如 '0.5' 满足条件的阈值
            'factor_list': [
                ('开盘至今涨幅', False, None, 1, '0945')  # 择时因子，格式为 (因子名称，排序方式，择时时间，因子权重，分钟数据配置)
            ],
            'params': 0.5  # 择时参数，定风波阈值。
        }
    }
]

excluded_boards = ["bj"]  # 排除板块，比如 cyb 表示创业板，kcb 表示科创板，bj 表示北交所
# excluded_boards = ["cyb", "kcb", "bj"]  # 同时过滤创业板和科创板和北交所

# 上市至今交易天数
days_listed = 250
# 整体资金使用率，也就是用于模拟的资金比例
total_cap_usage = 100 / 100  # 100%表示用全部的资金买入，如果是0.5就是使用一半的资金来模拟交易

# ====================================================================================================
# 4️⃣ 模拟交易配置
# 以下参数几乎不需要改动
# ====================================================================================================
initial_cash = 10_0000  # 初始资金10w
# initial_cash = 1_0000_0000  # 初始资金10w
# 手续费
c_rate = 1.2 / 10000
# 印花税
t_rate = 1 / 1000

# ====================================================================================================
# 5️⃣ 其他配置
# 以下参数几乎不需要改动
# ====================================================================================================
n_jobs = os.cpu_count() - 1

# ==== factor_col_limit 介绍 ====
factor_col_limit = 8  # 内存优化选项，一次性计算多少列因子。8 是16G电脑的推荐配置
# - 数字越大，计算速度越快，但同时内存占用也会增加。
# - 该数字是在 "因子数量 * 参数数量" 的基础上进行优化的。
#   - 例如，当你遍历 200 个因子，每个因子有 10 个参数，总共生成 2000 列因子。
#   - 如果 `factor_col_limit` 设置为 64，则计算会拆分为 ceil(2000 / 64) = 32 个批次，每次最多处理 64 列因子。
# - 以上数据仅供参考，具体值会根据机器配置、策略复杂性、回测周期等有所不同。建议大家根据实际情况，逐步测试自己机器的性能极限，找到适合的最优值。

# =====参数预检查=====
runtime_folder = get_folder_path(runtime_data_path, '运行缓存')
if Path(data_center_path).exists() is False:
    print(f'数据中心路径不存在：{data_center_path}，请检查配置或联系助教，程序退出')
    exit()

# 强制转换为 Path 对象
data_center_path = Path(data_center_path)
runtime_data_path = Path(runtime_data_path)
