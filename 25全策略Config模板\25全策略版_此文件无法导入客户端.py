small_list = [
    {
        'name': '小市值',
        'hold_period': 'W',
        'offset_list': [0],
        'select_num': 5,
        'cap_weight': 1,
        'rebalance_time': 'open',
        'factor_list': [('市值', True, '', 1)],
        'filter_list': [('成交额Mean', 5, 'val:>=5000_0000', True)]
    },
    {
        'name': '小市值_周黎明',
        'hold_period': 'W',
        'offset_list': [0],
        'select_num': 5,
        'cap_weight': 1,
        'rebalance_time': 'open',
        'factor_list': [('Ret', False, 5, 100),
                        ('Ret', False, 20, 0.2),
                        ('一级行业', False, '', 2),
                        ('市值', True, '', 1), ],
        'filter_list': [('成交额Mean', 5, 'val:>=5000_0000', True)]
    },
    {
        'name': '小市值_基本面优化',
        'hold_period': 'W',
        'offset_list': [0],
        'select_num': 5,
        'cap_weight': 1,
        'rebalance_time': 'open',
        'factor_list': [('市值', True, '', 1),
                        ('归母净利润同比增速', False, 60, 1), ],
        'filter_list': [('ROE', '单季', 'pct:<=0.8', False),
                        ('成交额Mean', 5, 'val:>=5000_0000', True)]
    },
    {
        'name': '小市值_量价优化',
        'hold_period': 'W',
        'offset_list': [0],
        'select_num': 5,
        'cap_weight': 1,
        'rebalance_time': 'open',
        'factor_list': [('Ret', True, 20, 1),
                        ('市值', True, '', 1),
                        ('成交额Std', True, 5, 1)],
        'filter_list': [('成交额Mean', 5, 'val:>=5000_0000', True)]
    },
    {
        'name': 'Alpha95策略',
        'hold_period': 'W',
        'offset_list': [0],
        'select_num': 5,
        'cap_weight': 1,
        'rebalance_time': 'open',
        'factor_list': [('成交额Std', True, 5, 1)],
        'filter_list': [('成交额Mean', 5, 'val:>=5000_0000', True),
                        ('近期停牌天数', 5, 'val:<1', True)]
    },
]

big_list = [
    {
        'name': '低估值策略',
        'hold_period': 'W',
        'offset_list': [0],
        'select_num': 5,
        'cap_weight': 1,
        'rebalance_time': 'open',
        'factor_list': [('EP', False, '全年', 1),
                        ('BP', False, '', 1)],
        'filter_list': []
    },
    {
        'name': '低波大市值',
        'hold_period': 'W',
        'offset_list': [0],
        'select_num': 5,
        'cap_weight': 1,
        'rebalance_time': 'open',
        "factor_list": [
            ('市值', False, '', 1),
            ('波动率', True, 250, 1),
        ],
        "filter_list": []
    },
]

medium_list = [
    {
        'name': '中等生策略',
        'hold_period': 'W',
        'offset_list': [0],
        'select_num': 5,
        'cap_weight': 1,
        'rebalance_time': 'open',
        'factor_list': [('指数相关性', False, ['sh000300', 20], 1),
                        ('指数相关性', False, ['sh932000', 20], 1),
                        ('指数相关性', False, ['sh000001', 20], 1),
                        ('指数相关性', False, ['sh000016', 20], 1),
                        ('指数相关性', False, ['sh000852', 20], 1),
                        ('指数相关性', False, ['sh000905', 20], 1),
                        ('指数相关性', False, ['sz399006', 20], 1), ],
        'filter_list': []
    },
    {
        'name': '中等生策略',
        'hold_period': 'W',
        'offset_list': [0],
        'select_num': 5,
        'cap_weight': 1,
        'rebalance_time': 'open',
        'factor_list': [('指数相关性', False, ['sh000016', 20], 1),
                        ('指数相关性', False, ['sz399006', 20], 1),
                        ],
        'filter_list': []
    },
    {
        'name': '中等生策略',
        'hold_period': 'W',
        'offset_list': [0],
        'select_num': 5,
        'cap_weight': 1,
        'rebalance_time': 'open',
        'factor_list': [('指数相关性', False, ['sh000300', 20], 1),
                        ('指数相关性', False, ['sh932000', 20], 1),
                        ],
        'filter_list': []
    },
]

other_list = [
    {
        'name': '镜重圆',
        'hold_period': 'W',
        'offset_list': [0],
        'select_num': 5,
        'cap_weight': 1,
        'rebalance_time': 'open',
        'factor_list': [('净利润_单季同比', False, '', 1),
                        ('ROE', False, '单季', 1),
                        ],
        'filter_list': [('BP', '', 'val:>1', False), ]
    }
]
