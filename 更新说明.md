# 邢不行® 策略分享会️

## 🔆 选股策略框架𝓟𝓻𝓸

### v1.4.2

> 发布时间: 2025-04-16

#### 更新内容

- 🎨 优化因子的分钟数据的逻辑，简化+加强，并解决了剑南老板遇到的无法实现的瓶颈
- 📦 支持更多的数据中心的数据导入。 `data-bridge`
- 🆕 新增对北交所的支持。
- 🔧 完善涨跌幅计算，和指标计算。
- ✨ 优化遍历逻辑，增强因子分析工具。
- 🧰 其他的一些小问题修复，顺便多喝了几杯咖啡…

#### 更新文件

- [`core`](core)目录
- [`config.py`](config.py)

-----

### v1.4.1

> 发布时间: 2025-04-07

#### 更新内容

- 🆕 同步定风波实盘逻辑
- 🆕 添加科创版、创业板过滤配置

#### 更新文件

- [`core`](core)目录
- [`config.py`](config.py)

### v1.4.0

> 发布时间: 2025-04-03

**✨ 超级重要版本更新**

#### 更新内容

- 🆕 全面支持盘中择时类策略回测（定风波系列），策略配置中新增 `timing` 字段，可灵活设置盘中择时信号的计算方式
- 🆕 引入全新模块 `信号库`，用于统一管理和计算各类择时信号
- 🔧 修复回测时间范围设置问题：此前默认与选股周期对齐，现在当 `end_date` 设置为 `None` 时，将自动与指数数据对齐，确保数据一致性
- 🔧 修复因提前筛选导致的核心因子计算交易日不连续的问题，提升复合因子计算的稳定性与准确性
- 新增 tool1 股票单因子分析工具
- 新增 tool2 股票双因子分析工具

#### 更新文件

- 强烈建议全量更新，如果不需要择时，那就停留在 `v1.3.0` 版本即可

---

### v1.3.0

> 发布时间: 2025-03-18

#### 更新内容

- 支持从5分钟收盘价或者15分钟收盘价的字段中新增日内的换仓时间
- 修复港股数据处fillna使用不正确可能导致的Waring
- 修复价格序列变动可能会引起的bug
- 去除分钟数据上的1500数据，因为该数据就是close，属于无效数据
- 修复加载分钟换仓时间和因子换仓时间可能会造成的数据冲突
- 针对ov_cols增加特别字段的处理逻辑

#### 更新文件

- 修改：
    - [`core/model/backtest_config.py`](core/model/backtest_config.py)
    - [`core/model/type_def.py`](core/model/type_def.py)
    - [`core/data_bridge.py`](core/data_bridge.py)
    - [`core/data_center.py`](core/data_center.py)
    - [`core/equity.py`](core/equity.py)
    - [`core/market_essentials.py`](core/market_essentials.py)
    - [`因子库分/分钟价格5m.py`](因子库/分钟价格5m.py)
    - [`因子库/分钟价格5m.py`](因子库/分钟价格5m.py)
    - [`更新说明.md`](更新说明.md)

---

### v1.2.0

> 发布时间：暂无发布时间

#### 更新内容

- 🚀 优化因子计算，加快计算速度

#### 更新文件

- [`select_stock.py`](core/select_stock.py)
- [`config.py`](config.py)

---

### v1.1.1

> 发布时间: 2025-02-07

#### 更新内容

- 🆕 系统添加“中性化函数”支持
- 🤖 新增外部数据导入
    - 分红数据
    - 15分钟收盘价
    - 5分钟收盘价
- 🏭 新增因子
    - 分钟价格15m([因子库/分钟价格15m.py](因子库/分钟价格15m.py))
    - 分钟价格5m([因子库/分钟价格5m.py](因子库/分钟价格5m.py))

#### 更新文件

- [`core/data_bridge.py`](core/data_bridge.py)
- [`core/market_essentials.py`](core/market_essentials.py)
- [`core/version.py`](core/version.py)
- [`更新说明.md`](更新说明.md)

----

### v1.1.0

> 发布时间: 2025-01-22

#### 更新内容

- 🦖 新增支持外部数据（目前仅限于我们数据中心中的大部分数据）
- ✨ 新增额外配置全息数据字段
- 🍁 延申FactorConfig的第四个权重字段，支持复杂策略的配置

#### 更新文件

🪄 如果你不想那么麻烦，替换 `core` 文件夹即可

- [`core -> model -> backtest_config.py`](core/model/backtest_config.py)
- [`core -> data_bridge.py`](core/data_bridge.py)
- [`core -> data_center.py`](core/data_center.py)
- [`core -> select_stock.py`](core/select_stock.py)
- [`core -> version.py`](core/version.py)
- [`更新说明.md`](更新说明.md)

----

### v1.0.1

> 发布时间: 2025-01-14

#### 更新内容

- 🐞 修复了过滤因子在复合参数下报错的问题
- ✨ 优化了仓位资金占比逻辑，内核实现“动态杠杆”模拟
- 🎛️ 调整多策略归一化逻辑，当且仅当整体配置 > 1 时，才进行归一化

#### 更新文件

- [`core -> model -> backtest_config.py`](core/model/backtest_config.py)
- [`core -> model -> strategy_config.py`](core/model/strategy_config.py)
- [`core -> equity.py`](core/equity.py)
- [`core -> version.py`](core/version.py)
- [`更新说明.md`](更新说明.md)

----

### v1.0.0

> 发布时间: 2025-01-10
