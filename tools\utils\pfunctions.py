"""
2024分享会
author: 邢不行
微信: xbx6660
"""
import math
import os
import platform
import webbrowser
from pathlib import Path
from types import SimpleNamespace
from typing import List, Optional

import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objs as go
from plotly import subplots
from plotly.offline import plot
from plotly.subplots import make_subplots


def float_num_process(num, return_type=float, keep=2, max=5):
    """
    针对绝对值小于1的数字进行特殊处理，保留非0的N位（N默认为2，即keep参数）
    输入  0.231  输出  0.23
    输入  0.0231  输出  0.023
    输入  0.00231  输出  0.0023
    如果前面max个都是0，直接返回0.0
    :param num: 输入的数据
    :param return_type: 返回的数据类型，默认是float
    :param keep: 需要保留的非零位数
    :param max: 最长保留多少位
    :return:
        返回一个float或str
    """

    # 如果输入的数据是0，直接返回0.0
    if num == 0.:
        return 0.0

    # 绝对值大于1的数直接保留对应的位数输出
    if abs(num) > 1:
        return round(num, keep)
    # 获取小数点后面有多少个0
    zero_count = -int(math.log10(abs(num)))
    # 实际需要保留的位数
    keep = min(zero_count + keep, max)

    # 如果指定return_type是float，则返回float类型的数据
    if return_type == float:
        return round(num, keep)
    # 如果指定return_type是str，则返回str类型的数据
    else:
        return str(round(num, keep))


# 绘制IC图
def draw_ic_plotly(x, y1, y2, title='', info='', pic_size=[1800, 600]):
    """
    IC画图函数
    :param x: x轴，时间轴
    :param y1: 第一个y轴，每周期的IC
    :param y2: 第二个y轴，累计的IC
    :param title: 图标题
    :param info: IC字符串
    :param pic_size: 图片大小
    :return:
    """

    # 创建子图
    fig = make_subplots(rows=1, cols=1, specs=[[{"secondary_y": True}]])

    # 添加柱状图轨迹
    fig.add_trace(
        go.Bar(
            x=x,  # X轴数据
            y=y1,  # 第一个y轴数据
            name=y1.name,  # 第一个y轴的名字
            marker_color='orange',  # 设置颜色
            marker_line_color='orange'  # 设置柱状图边框的颜色
        ),
        row=1, col=1, secondary_y=False
    )

    # 添加折线图轨迹
    fig.add_trace(
        go.Scatter(
            x=x,  # X轴数据
            y=y2,  # 第二个y轴数据
            text=y2,  # 第二个y轴的文本
            name=y2.name,  # 第二个y轴的名字
            marker_color='blue'  # 设置颜色
        ),
        row=1, col=1, secondary_y=True
    )

    # 更新布局
    fig.update_layout(
        plot_bgcolor='rgb(255, 255, 255)',  # 设置绘图区背景色
        width=pic_size[0],  # 调整宽度
        height=pic_size[1],  # 调整高度
        title={
            'text': title,  # 标题文本
            'x': 0.377,  # 标题相对于绘图区的水平位置
            'y': 0.9,  # 标题相对于绘图区的垂直位置
            'xanchor': 'center',  # 标题的水平对齐方式
            'font': {'color': 'green', 'size': 20}  # 标题的颜色和大小
        },
        xaxis=dict(domain=[0.0, 0.73]),  # 设置 X 轴的显示范围
        legend=dict(
            x=0.8,  # 图例相对于绘图区的水平位置
            y=1.0,  # 图例相对于绘图区的垂直位置
            bgcolor='white',  # 图例背景色
            bordercolor='gray',  # 图例边框颜色
            borderwidth=1  # 图例边框宽度
        ),
        annotations=[
            dict(
                x=x.iloc[len(x) // 2],  # 文字的 x 轴位置
                y=0.6,  # 文字的 y 轴位置
                text=info,  # 文字内容
                showarrow=False,  # 是否显示箭头
                font=dict(
                    size=14  # 设置文字的字体大小
                )
            )
        ],
        hovermode="x unified",
        hoverlabel=dict(bgcolor='rgba(255,255,255,0.5)', )
    )

    # 将图表转换为 HTML 格式
    return_fig = plot(fig, include_plotlyjs=True, output_type='div')

    return return_fig


# 绘制IC月历图
def draw_hot_plotly(x, y, z, title='', pic_size=[1800, 600]):
    """
    IC月历画图函数
    :param x: X轴：月份
    :param y: Y轴：年份
    :param z: Z轴：IC数据
    :param title: IC月历标题名
    :param pic_size: 图片大小
    :return:
        返回IC月历图
    """

    # 创建子图
    fig = make_subplots()

    # 添加热力图轨迹
    fig.add_trace(
        go.Heatmap(
            x=x,  # X轴数据
            y=y,  # Y轴数据
            z=z.values,  # 绘制热力图的数据
            text=z.values,  # 热力图中的数值
            colorscale=[
                [0, 'green'],  # 自定义的颜色点
                [0.5, 'yellow'],
                [1, 'red']
            ],
            colorbar=dict(
                x=0.82,
                y=0.47,
                len=1
            )
        ),
        row=1, col=1
    )

    # 更新布局
    fig.update_layout(
        plot_bgcolor='rgb(255, 255, 255)',  # 设置绘图区背景色
        width=pic_size[0],  # 宽度
        height=pic_size[1],  # 高度
        title={
            'text': title,  # 标题文本
            'x': 0.377,  # 标题相对于绘图区的水平位置
            'y': 0.9,  # 标题相对于绘图区的垂直位置
            'xanchor': 'center',  # 标题的水平对齐方式
            'font': {'color': 'green', 'size': 20}  # 标题的颜色和大小
        },
        xaxis=dict(
            domain=[0.0, 0.73],  # 设置 X 轴的显示范围
            showticklabels=True,
            dtick=1
        )
    )

    z_ = z.map(float_num_process, na_action='ignore')

    for i in range(z.shape[1]):
        for j in range(z.shape[0]):
            fig.add_annotation(x=i, y=j, text=z_.iloc[j, i], showarrow=False)

    # 将图表转换为 HTML 格式
    return_fig = plot(fig, include_plotlyjs=True, output_type='div')

    return return_fig


# 绘制柱状图
def draw_bar_plotly(x, y, title='', pic_size=[1800, 600], y_range=False):
    """
    柱状图画图函数
    :param x: 放到X轴上的数据
    :param y: 放到Y轴上的数据
    :param title: 图标题
    :param pic_size: 图大小
    :return:
        返回柱状图
    """

    # 创建子图
    fig = make_subplots()

    y_ = y.map(float_num_process, na_action='ignore')

    # 添加柱状图轨迹
    fig.add_trace(go.Bar(
        x=x,  # X轴数据
        y=y,  # Y轴数据
        text=y_,  # Y轴文本
        name=x.name  # 图里名字
    ), row=1, col=1)

    # 更新X轴的tick
    fig.update_xaxes(
        tickmode='array',
        tickvals=x
    )
    # 设置Y轴范围
    if y_range:
        fig.update_yaxes(range=[y_range[0], y_range[1]])
    # 更新布局
    fig.update_layout(
        plot_bgcolor='rgb(255, 255, 255)',  # 设置绘图区背景色
        width=pic_size[0],  # 宽度
        height=pic_size[1],  # 高度
        title={
            'text': title,  # 标题文本
            'x': 0.377,  # 标题相对于绘图区的水平位置
            'y': 0.9,  # 标题相对于绘图区的垂直位置
            'xanchor': 'center',  # 标题的水平对齐方式
            'font': {'color': 'green', 'size': 20}  # 标题的颜色和大小
        },
        xaxis=dict(domain=[0.0, 0.73]),  # 设置 X 轴的显示范围
        showlegend=True,  # 是否显示图例
        legend=dict(
            x=0.8,  # 图例相对于绘图区的水平位置
            y=1.0,  # 图例相对于绘图区的垂直位置
            bgcolor='white',  # 图例背景色
            bordercolor='gray',  # 图例边框颜色
            borderwidth=1  # 图例边框宽度
        )
    )

    # 将图表转换为 HTML 格式
    return_fig = plot(fig, include_plotlyjs=True, output_type='div')

    return return_fig


# 绘制折线图
def draw_line_plotly(x, y1, y2=[], update_xticks=False, if_log='False', title='', pic_size=[1800, 600]):
    """
    折线画图函数
    :param x: X轴数据
    :param y1: 左轴数据
    :param y2: 右轴数据
    :param update_xticks: 是否更新x轴刻度
    :param if_log: 是否需要log轴
    :param title: 图标题
    :param pic_size: 图片大小
    :return:
        返回折线图
    """

    # 创建子图
    fig = make_subplots(rows=1, cols=1, specs=[[{"secondary_y": True}]])

    # 添加折线图轨迹
    for col in y1.columns:
        fig.add_trace(
            go.Scatter(
                x=x,  # X轴数据
                y=y1[col],  # Y轴数据
                name=col,  # 图例名字
                line={'width': 2}  # 调整线宽
            ),
            row=1, col=1, secondary_y=False
        )

    if len(y2):
        fig.add_trace(
            go.Scatter(
                x=x,  # X轴数据
                y=y2,  # 第二个Y轴的数据
                name=y2.name,  # 图例名字
                line={'color': 'red', 'dash': 'dot', 'width': 2}  # 调整折现的样式，红色、点图、线宽
            ),
            row=1, col=1, secondary_y=True
        )

    # 如果是画分组持仓走势图的话，更新xticks
    if update_xticks:
        fig.update_xaxes(
            tickmode='array',
            tickvals=x
        )

    # 更新布局
    fig.update_layout(
        plot_bgcolor='rgb(255, 255, 255)',  # 设置绘图区背景色
        width=pic_size[0],
        height=pic_size[1],
        title={
            'text': f'{title}',  # 标题文本
            'x': 0.377,  # 标题相对于绘图区的水平位置
            'y': 0.9,  # 标题相对于绘图区的垂直位置
            'xanchor': 'center',  # 标题的水平对齐方式
            'font': {'color': 'green', 'size': 20}  # 标题的颜色和大小
        },
        xaxis=dict(domain=[0.0, 0.73]),  # 设置 X 轴的显示范围
        legend=dict(
            x=0.8,  # 图例相对于绘图区的水平位置
            y=1.0,  # 图例相对于绘图区的垂直位置
            bgcolor='white',  # 图例背景色
            bordercolor='gray',  # 图例边框颜色
            borderwidth=1  # 图例边框宽度
        ),
        hovermode="x unified",
        hoverlabel=dict(bgcolor='rgba(255,255,255,0.5)', )
    )
    # 添加log轴
    if if_log:
        fig.update_layout(
            updatemenus=[
                dict(
                    buttons=[
                        dict(label="线性 y轴",
                             method="relayout",
                             args=[{"yaxis.type": "linear"}]),
                        dict(label="Log y轴",
                             method="relayout",
                             args=[{"yaxis.type": "log"}]),
                    ])], )

    # 将图表转换为 HTML 格式
    return_fig = plot(fig, include_plotlyjs=True, output_type='div')

    return return_fig


def draw_double_bar_plotly(x, y1, y2, title='', info='', pic_size=[1800, 600]):
    """
    双柱状图的画图函数
    :param x: X轴数据
    :param y1: 第一个柱状图的数据
    :param y2: 第二个柱状图的数据
    :param title: 标题名
    :param info: 需要在图片上加的信息
    :param pic_size: 图片大小
    :return:
        返回双柱状图数据
    """

    # 创建子图
    fig = make_subplots()

    # 转换数据，保留小数点位数
    y1_ = y1.map(float_num_process, na_action='ignore')
    y2_ = y2.map(float_num_process, na_action='ignore')

    # 添加第一组柱状图
    fig.add_trace(go.Bar(x=x, y=y1, name=y1.name, text=y1_, marker_color='red'))

    # 添加第二组柱状图
    fig.add_trace(go.Bar(x=x, y=y2, name=y2.name, text=y2_, marker_color='green'))

    # 更新X轴的tick
    fig.update_xaxes(
        tickmode='array',
        tickvals=x
    )

    # 更新布局
    fig.update_layout(
        plot_bgcolor='rgb(255, 255, 255)',  # 设置绘图区背景色
        width=pic_size[0],  # 宽度
        height=pic_size[1],  # 高度
        title={
            'text': title,  # 标题文本
            'x': 0.377,  # 标题相对于绘图区的水平位置
            'y': 0.9,  # 标题相对于绘图区的垂直位置
            'xanchor': 'center',  # 标题的水平对齐方式
            'font': {'color': 'green', 'size': 20}  # 标题的颜色和大小
        },
        xaxis=dict(domain=[0.0, 0.73]),  # 设置 X 轴的显示范围
        legend=dict(
            x=0.8,  # 图例相对于绘图区的水平位置
            y=1.0,  # 图例相对于绘图区的垂直位置
            bgcolor='white',  # 图例背景色
            bordercolor='gray',  # 图例边框颜色
            borderwidth=1  # 图例边框宽度
        ),
        annotations=[
            dict(
                x=0.36,  # 文字的 x 轴位置
                y=1,  # 文字的 y 轴位置
                xref='paper',  # 指定 x 坐标的参考为整个绘图区域
                yref='paper',  # 指定 y 坐标的参考为整个绘图区域
                text=info,  # 文字内容
                showarrow=False,  # 是否显示箭头
                font=dict(size=16, color='black')
            )
        ]
    )

    # 将图表转换为 HTML 格式
    return_fig = plot(fig, include_plotlyjs=True, output_type='div')

    return return_fig


def merge_html(folder_path, fig_list, strategy_file, bbs_id, title):
    # 创建合并后的网页文件
    merged_html_file = folder_path / f'{strategy_file}.html'

    # 创建自定义HTML页面，嵌入fig对象的HTML内容
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
    <style>
        .body {{
            width: 2000px;
            height:100%;
            }},
        .figure-container {{
            display: flex;
            flex-direction: column;
            align-items: center;
        }}
    </style>
    </head>
    <body>
        <h1 style="hight:45px;"></h1>
    <h1 style="margin-left:90px; color: black; font-size: 20px;">{title}</h1>
    <h3 style="margin-left:60%; margin-top:10px; font-size: 20px;"><a href="https://bbs.quantclass.cn/thread/{bbs_id}" target="_blank">如何看懂这些图?</a></h3>
    """
    for fig in fig_list:
        html_content += f"""
        <div class="figure-container">
            {fig}
        </div>
        """
    html_content += '</body> </html>'

    # 保存自定义HTML页面
    with open(merged_html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)

    res = os.system('start ' + str(merged_html_file))
    if res != 0:
        os.system('open ' + str(merged_html_file))


def draw_three_bar_plotly(x, y1, y2, y3, title='', info='', pic_size=[1800, 600]):
    """
    双柱状图的画图函数
    :param x: X轴数据
    :param y1: 第一个柱状图的数据
    :param y2: 第二个柱状图的数据
    :param title: 标题名
    :param info: 需要在图片上加的信息
    :param pic_size: 图片大小
    :return:
        返回双柱状图数据
    """

    # 创建子图
    fig = make_subplots()

    # 转换数据，保留小数点位数
    y1_ = y1.map(float_num_process, na_action='ignore')
    y2_ = y2.map(float_num_process, na_action='ignore')
    y3_ = y3.map(float_num_process, na_action='ignore')

    # 添加第一组柱状图
    fig.add_trace(go.Bar(x=x, y=y1, name=y1.name, text=y1_, marker_color='red'))

    # 添加第二组柱状图
    fig.add_trace(go.Bar(x=x, y=y2, name=y2.name, text=y2_, marker_color='green'))

    # 添加第三组柱状图
    fig.add_trace(go.Bar(x=x, y=y3, name=y3.name, text=y3_, marker_color='blue'))

    # 更新X轴的tick
    fig.update_xaxes(
        tickmode='array',
        tickvals=x
    )

    # 更新布局
    fig.update_layout(
        plot_bgcolor='rgb(255, 255, 255)',  # 设置绘图区背景色
        width=pic_size[0],  # 宽度
        height=pic_size[1],  # 高度
        title={
            'text': title,  # 标题文本
            'x': 0.377,  # 标题相对于绘图区的水平位置
            'y': 0.9,  # 标题相对于绘图区的垂直位置
            'xanchor': 'center',  # 标题的水平对齐方式
            'font': {'color': 'green', 'size': 20}  # 标题的颜色和大小
        },
        xaxis=dict(domain=[0.0, 0.73]),  # 设置 X 轴的显示范围
        legend=dict(
            x=0.8,  # 图例相对于绘图区的水平位置
            y=1.0,  # 图例相对于绘图区的垂直位置
            bgcolor='white',  # 图例背景色
            bordercolor='gray',  # 图例边框颜色
            borderwidth=1  # 图例边框宽度
        ),
        annotations=[
            dict(
                x=0.36,  # 文字的 x 轴位置
                y=1,  # 文字的 y 轴位置
                xref='paper',  # 指定 x 坐标的参考为整个绘图区域
                yref='paper',  # 指定 y 坐标的参考为整个绘图区域
                text=info,  # 文字内容
                showarrow=False,  # 是否显示箭头
                font=dict(size=16, color='black')
            )
        ]
    )

    # 将图表转换为 HTML 格式
    return_fig = plot(fig, include_plotlyjs=True, output_type='div')

    return return_fig


def draw_params_bar_plotly(df: pd.DataFrame, title: str):
    draw_df = df.copy()
    rows = len(draw_df.columns)
    s = (1 / (rows - 1)) * 0.5
    fig = subplots.make_subplots(rows=rows, cols=1, shared_xaxes=True, shared_yaxes=True, vertical_spacing=s)

    for i, col_name in enumerate(draw_df.columns):
        trace = go.Bar(x=draw_df.index, y=draw_df[col_name], name=f"{col_name}")
        fig.add_trace(trace, i + 1, 1)
        # 更新每个子图的x轴属性
        fig.update_xaxes(showticklabels=True, row=i + 1, col=1)  # 旋转x轴标签以避免重叠

    # 更新每个子图的y轴标题
    for i, col_name in enumerate(draw_df.columns):
        fig.update_xaxes(title_text=col_name, row=i + 1, col=1)

    fig.update_layout(height=200 * rows, showlegend=True, title={
        'text': f'{title}',  # 标题文本
        'x': 0.5,
        'yanchor': 'top',
        'font': {'color': 'green', 'size': 20}  # 标题的颜色和大小
    }, )

    return_fig = plot(fig, include_plotlyjs=True, output_type='div')
    return return_fig


def draw_params_heatmap_plotly(df, title=''):
    """
    生成热力图
    """
    draw_df = df.copy()

    draw_df.replace(np.nan, '', inplace=True)
    # 修改temp的index和columns为str
    draw_df.index = draw_df.index.astype(str)
    draw_df.columns = draw_df.columns.astype(str)

    fig = px.imshow(
        draw_df,
        title=title,
        text_auto=True,
        color_continuous_scale='Viridis',
        # aspect='auto'
    )

    # 关键修改：启用响应式布局
    fig.update_layout(
        paper_bgcolor='rgba(255,255,255,1)',
        plot_bgcolor='rgba(255,255,255,1)',
        autosize=True,
        margin=dict(l=20, r=20, t=40, b=20),  # 减少边距

        title={
            'text': f'{title}',
            'y': 0.95,
            'x': 0.5,
            'font': {'color': 'green', 'size': 16}  # 标题字号适当减小
        }
    )

    return plot(
        fig,
        include_plotlyjs=True,
        output_type='div',
        config={
            'responsive': True,  # 启用响应式配置
            'displayModeBar': True  # 显示工具栏
        }
    )


def draw_hedge_signal_plotly(df, index_df, save_path, title, trade_df, _res_loc, add_factor_main_list,
                             add_factor_sub_list, color_dict, pic_size=[1880, 1000]):
    # # 主图增加,均为折线图。除指数外，均需要在cal_factors中可被计算出
    # add_factor_main_list = [{'因子名称': '指数', '次坐标轴': True},
    #                         {'因子名称': '5日均线', '次坐标轴': False},
    #                         {'因子名称': '20日均线', '次坐标轴': False}
    #                         ]
    #
    # # 附图增加，一个dict为一个子图
    # # 因子名称的list大于1个值，则会被画在同一个图中，没用次坐标轴概念
    # # 图形样式有且仅有三种选择K线图\柱状图\折线图
    # add_factor_sub_list = [{'因子名称': ['指数'], '图形样式': 'K线图'},
    #                        {'因子名称': ['成交额'], '图形样式': '柱状图'},
    #                        {'因子名称': ['Ret_5'], '图形样式': '折线图'},
    #                        {'因子名称': ['筹码集中度', '价格分位数'], '图形样式': '折线图'},
    #

    # 随机颜色的列表
    color_list = ['#feb71d', '#dc62af', '#4d50bb', '#f0eb8d', '#018b96', '#e7adea']
    color_i = 0
    for each_factor in add_factor_main_list:
        if each_factor['因子名称'] not in color_dict.keys():
            color_dict[each_factor['因子名称']] = color_list[color_i % len(color_list)]
            color_i += 1
    for each_sub in add_factor_sub_list:
        for each_factor in each_sub['因子名称']:
            if each_factor not in color_dict.keys():
                color_dict[each_factor] = color_list[color_i % len(color_list)]
                color_i += 1

    time_data = df['交易日期']
    # 增加多少个子图
    add_rows = len(add_factor_sub_list)

    # 750是主图，add_rows是子图个数。
    pic_size[1] = max(1000, 750 + add_rows * 250)

    # 主图有没有副轴
    have_secondary_y = any(each_factor.get('次坐标轴', False) for each_factor in add_factor_main_list)

    # 构建画布左轴
    fig = make_subplots(rows=1 + len(add_factor_sub_list), cols=1, shared_xaxes=True,
                        specs=[[{"secondary_y": have_secondary_y}]] + add_rows * [[{"secondary_y": False}]])

    # 绘制k线图
    fig.add_trace(go.Candlestick(
        x=time_data,
        open=df['开盘价_复权'],  # 字段数据必须是元组、列表、numpy数组、或者pandas的Series数据
        high=df['最高价_复权'],
        low=df['最低价_复权'],
        close=df['收盘价_复权'],
        name='k线',
        increasing_line_color='#c13945',  # 涨的K线颜色
        decreasing_line_color='#51b82b',  # 跌的K线颜色
        text=[f'日期: {date}' for date in time_data.dt.date.astype(str)],
    ), row=1, col=1)
    # 绘制主图上其它因子（包括指数或者均线）
    for each_factor in add_factor_main_list:
        if each_factor['因子名称'] == '指数':
            fig.add_trace(
                go.Scatter(
                    x=index_df['交易日期'],
                    y=index_df['close'],
                    name='指数',
                    marker_color=color_dict['指数']
                ),
                row=1, col=1, secondary_y=each_factor['次坐标轴']
            )
        else:
            fig.add_trace(
                go.Scatter(
                    x=time_data,
                    y=df[each_factor['因子名称']],
                    name=each_factor['因子名称'],
                    marker_color=color_dict[each_factor['因子名称']]
                ),
                row=1, col=1, secondary_y=each_factor['次坐标轴']
            )

    # 更新x轴设置，非交易日在X轴上排除
    date_range = pd.date_range(start=time_data.min(), end=time_data.max(), freq='D')
    miss_dates = date_range[~date_range.isin(time_data)].to_list()
    fig.update_xaxes(rangebreaks=[dict(values=miss_dates)])

    # 标记买卖点的数据，绘制在最后
    mark_point_list = []
    # 生成不同offset箭头颜色映射
    offset_types = sorted(trade_df['_持仓周期'].unique())
    colors = px.colors.qualitative.Plotly + px.colors.qualitative.Dark24
    color_map = {otype: colors[i % len(colors)] for i, otype in enumerate(offset_types)}

    for i in df[(df['买入时间'].notna()) | (df['卖出时间'].notna())].index:
        open_signal = df.loc[i, '买入时间']
        close_signal = df.loc[i, '卖出时间']

        # 同时存在买入和卖出信号
        if pd.notnull(open_signal) and pd.notnull(close_signal):

            open_signal_offset = trade_df.loc[trade_df['买入日期'] == df.loc[i, '交易日期'], '_持仓周期']
            if len(open_signal_offset) >= 2:
                open_signal_offset = open_signal_offset.str.cat(sep='_')
            else:
                open_signal_offset = open_signal_offset.iloc[0]
            close_signal_offset = trade_df.loc[trade_df['卖出日期'] == df.loc[i, '交易日期'], '_持仓周期']
            if len(close_signal_offset) >= 2:
                close_signal_offset = close_signal_offset.str.cat(sep='_')
            else:
                close_signal_offset = close_signal_offset.iloc[0]
            # 添加买入箭头
            mark_point_list.append({
                'x': df.at[i, '交易日期'],
                'y': df.at[i, '最低价_复权'] * 0.99,
                'showarrow': True,
                'text': f"B_{open_signal_offset}",
                'ax': 0,
                'ay': 50,  # 箭头向下
                'arrowhead': 3,
                'arrowcolor': color_map.get(open_signal_offset, 'black'),
            })
            # 添加卖出箭头
            mark_point_list.append({
                'x': df.at[i, '交易日期'],
                'y': df.at[i, '最高价_复权'] * 1.01,
                'showarrow': True,
                'text': f"S_{close_signal_offset}",
                'ax': 0,
                'ay': -50,  # 箭头向上
                'arrowhead': 1,
                'arrowcolor': color_map.get(close_signal_offset, 'black')
            })

        # 只有买入信号
        elif pd.notnull(open_signal) and pd.isnull(close_signal):
            open_signal_offset = trade_df.loc[trade_df['买入日期'] == df.loc[i, '交易日期'], '_持仓周期']
            if len(open_signal_offset) >= 2:
                open_signal_offset = open_signal_offset.str.cat(sep='_')
            else:
                open_signal_offset = open_signal_offset.iloc[0]
            mark_point_list.append({
                'x': df.at[i, '交易日期'],
                'y': df.at[i, '最低价_复权'] * 0.99,
                'showarrow': True,
                'text': f"B_{open_signal_offset}",
                'ax': 0,
                'ay': 50,
                'arrowhead': 3,
                'arrowcolor': color_map.get(open_signal_offset, 'black')
            })

        # 只有卖出信号
        elif pd.notnull(close_signal) and pd.isnull(open_signal):
            close_signal_offset = trade_df.loc[trade_df['卖出日期'] == df.loc[i, '交易日期'], '_持仓周期']
            if len(close_signal_offset) >= 2:
                close_signal_offset = close_signal_offset.str.cat(sep='_')
            else:
                close_signal_offset = close_signal_offset.iloc[0]
            mark_point_list.append({
                'x': df.at[i, '交易日期'],
                'y': df.at[i, '最高价_复权'] * 1.01,
                'showarrow': True,
                'text': f"S_{close_signal_offset}",
                'ax': 0,
                'ay': -50,
                'arrowhead': 1,
                'arrowcolor': color_map.get(close_signal_offset, 'black')
            })

    # 更新画布布局，把买卖点标记上、把主图的大小调整好
    fig.update_layout(annotations=mark_point_list, template="none", width=pic_size[0], height=pic_size[1],
                      title_text=title, hovermode='x',
                      yaxis=dict(domain=[1 - 750 / pic_size[1], 1.0]), xaxis=dict(domain=[0.0, 0.73]),
                      xaxis_rangeslider_visible=False,
                      )
    # 主图有副轴，就更新
    if have_secondary_y:
        fig.update_layout(yaxis2=dict(domain=[1 - 750 / pic_size[1], 1.0]), xaxis2=dict(domain=[0.0, 0.73]))

    # ==绘制子图
    row = 2  # 1是第一个主图，所以不用管
    # 子图的范围都做算好
    y_domains = [[1 - (1000 + 250 * i) / pic_size[1], 1 - (750 + 250 * i) / pic_size[1]] for i in
                 range(0, add_rows)]
    x_domains = [[0.0, 0.73] for _ in range(0, add_rows)]
    # 做每个子图
    for each_factor in add_factor_sub_list:
        graphicStyle = each_factor['图形样式'].upper()
        for each_sub_factor in each_factor['因子名称']:
            if graphicStyle == '柱状图':
                fig.add_trace(go.Bar(x=time_data, y=df[each_sub_factor], name=each_sub_factor,
                                     marker_color=color_dict[each_sub_factor]), row=row, col=1)

            elif graphicStyle == '折线图':
                fig.add_trace(go.Scatter(x=time_data, y=df[each_sub_factor], name=each_sub_factor,
                                         marker_color=color_dict[each_sub_factor]), row=row, col=1)

            elif graphicStyle == 'K线图':
                fig.add_trace(go.Candlestick(
                    x=index_df['交易日期'],
                    open=index_df['open'],  # 字段数据必须是元组、列表、numpy数组、或者pandas的Series数据
                    high=index_df['high'],
                    low=index_df['low'],
                    close=index_df['close'],
                    name='指数',
                    increasing_line_color='#c13945',  # 涨的K线颜色
                    decreasing_line_color='#51b82b',  # 跌的K线颜色
                ), row=row, col=1)
                fig.update_xaxes(rangeslider_visible=False, row=row, col=1)
        fig.update_yaxes(dict(domain=y_domains[row - 2]), row=row)
        fig.update_xaxes(dict(domain=x_domains[row - 2]), row=row)
        fig.update_yaxes(title_text='、'.join(each_factor['因子名称']) + '因子', row=row, col=1)
        row += 1

    # 做两个信息表放到旁边
    res_loc = _res_loc.copy()
    res_loc[['累计持股收益', '次均收益率']] = res_loc[['累计持股收益', '次均收益率']].apply(
        lambda x: str(round(100 * x, 3)) + '%' if isinstance(x, float) else x)
    table_trace = go.Table(header=dict(
        values=[[title.split('_')[1]], [title.split('_')[0]]]),
        cells=dict(
            values=[res_loc.index.to_list()[2:-1], res_loc.to_list()[2:-1]]),
        domain=dict(x=[0.85, 1], y=[1 - 500 / pic_size[1], 1 - 100 / pic_size[1]]),
    )
    fig.add_trace(table_trace)
    table_trace = go.Table(header=dict(values=list(['持仓周期', '买入日期', '卖出日期', '买入价', '卖出价', '收益率'])),
                           cells=dict(
                               values=[trade_df['持仓周期'], trade_df['买入日期'].dt.date, trade_df['卖出日期'].dt.date,
                                       trade_df['买入价'],
                                       trade_df['卖出价'], trade_df['收益率']]),
                           domain=dict(x=[0.75, 1.0], y=[0.1, 1 - 500 / pic_size[1]]))
    fig.add_trace(table_trace)

    # 图例的位置调整
    fig.update_layout(legend=dict(x=0.75, y=1))

    # 保存路径
    save_path = save_path + title + '.html'
    plot(figure_or_data=fig, filename=save_path, auto_open=False)


def show_without_plot_native_show(fig, save_path: str | Path):
    save_path = save_path.absolute()
    print('⚠️ 因为新版pycharm默认开启sci-view功能，导致部分同学会在.show()的时候假死')
    print(f'因此我们会先保存HTML到: {save_path}, 然后调用默认浏览器打开')
    fig.write_html(save_path)

    """
    跨平台在默认浏览器中打开 URL 或文件
    """
    system_name = platform.system()  # 检测操作系统
    if system_name == "Darwin":  # macOS
        os.system(f'open "" "{save_path}"')
    elif system_name == "Windows":  # Windows
        os.system(f'start "" "{save_path}"')
    elif system_name == "Linux":  # Linux
        os.system(f'xdg-open "" "{save_path}"')
    else:
        # 如果不确定操作系统，尝试使用 webbrowser 模块
        webbrowser.open(save_path)


def merge_html_flexible(
        fig_list: List[str],
        html_path: str,
        title: Optional[str] = None,
        link_url: Optional[str] = None,
        link_text: Optional[str] = None,
        show: bool = True,
):
    """
    将多个Plotly图表合并到一个HTML文件，并允许灵活配置标题、副标题和链接

    :param fig_list: 包含Plotly图表HTML代码的列表
    :param html_path: 输出的HTML文件路径
    :param title: 主标题内容（例如"因子分析报告"）
    :param link_url: 右侧链接的URL地址
    :param link_text: 右侧链接的显示文本
    :param show: 是否自动打开HTML文件
    :return: 生成的HTML文件路径
    :raises OSError: 文件操作失败时抛出
    """

    # 构建header部分
    header_html = []
    if title:
        header_html.append(
            f'<div class="report-title">{title}</div>'
        )

    if link_url and link_text:
        header_html.append(
            f'<a href="{link_url}" class="report-link" target="_blank">{link_text} →</a>'
        )

    # 组合header部分
    header_str = ""
    if header_html:
        header_str = f'<div class="header">{"".join(header_html)}</div>'

    # 构建完整HTML内容
    html_template = f"""<!DOCTYPE html>
    <html>
    <head>
        <style>

            .header {{
                display: flex;
                justify-content: space-between;  /* 自动分配两端对齐 */
                align-items: center;
                padding: 20px 40px;  /* 横向增加内边距 */
            }}

            .figure-container {{
                width: 90%;
                margin: 20px auto;
            }}

            .report-title {{
                font-size: 20px;
                color: #2c3e50;
                margin-right: 200px
            }}

            .report-link {{
                font-size: 20px;
                text-decoration: none;
                color: #3498db;
                font-weight: 500;
                 margin-right: 300px;  /* 可选：添加右侧边距 */
            }}

            body {{
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 0;
            }}
        </style>
    </head>
    <body>
        {header_str}
        <div class="charts-wrapper">
            {"".join(f'<div class="figure-container">{fig}</div>' for fig in fig_list)}
        </div>
    </body>
    </html>
    """

    # 自动打开HTML文件
    if show:
        # 定义局部的 write_html 函数，并包装为具有 write_html 属性的对象
        def write_html(file_path: Path):
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(html_template)

        wrapped_html = SimpleNamespace(write_html=write_html)
        show_without_plot_native_show(wrapped_html, Path(html_path))
