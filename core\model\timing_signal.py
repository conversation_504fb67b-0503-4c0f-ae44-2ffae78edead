"""
邢不行｜策略分享会
选股策略框架𝓟𝓻𝓸

版权所有 ©️ 邢不行
微信: xbx1717

本代码仅供个人学习使用，未经授权不得复制、修改或用于商业用途。

Author: 邢不行
"""
from dataclasses import dataclass, field
from typing import List, Dict, Callable

from core.model.factor_config import FactorConfig, parse_param


@dataclass
class TimingSignal:
    name: str = 'TimingSignal'  # 信号名称
    limit: int | float = 100  # 因子计算的股票范围 例如 100 表示复合因子前50个股票择时，0.5 表示前50%的股票择时，0 表示全部股票择时（不建议）；
    factor_list: List[FactorConfig] = field(default_factory=list)  # 信号因子
    params: tuple = ()  # 信号参数
    signal_time: str = 'close'  # 信号时间
    recall_days = 256  # 回溯多久的历史数据，因子rolling越大，参数越大，速度也会越慢

    # 策略函数
    funcs: Dict[str, Callable] = field(default_factory=dict)

    @classmethod
    def init(cls, **config):
        config['factor_list'] = FactorConfig.parse_list(config.get('factor_list', []), False)
        config['params'] = parse_param(config.get('params', ()))
        timing_signal = cls(**config)

        if timing_signal.min_list:  # 有分钟数据的因子的话，会自动获取最大值，否则默认为close
            timing_signal.signal_time = max(timing_signal.min_list)

        return timing_signal

    @property
    def min_list(self):
        return tuple(sorted(set([m for f in self.factor_list for m in f.minutes if str(m).isdigit()])))

    def __repr__(self) -> str:
        return f"{self.name}_{self.signal_time}，因子{self.factor_list}，参数{self.params}"
